# Test script to compare /analyze and /detailed-analyze endpoints
# This script tests both endpoints with the same image URL to verify they give consistent results

$imageUrl = "https://imagedetectionv2.blob.core.windows.net/images/068b6a1c63b377698000ab8757617939"
$baseUrl = "http://localhost:8080"

Write-Host "Testing both endpoints with image: $imageUrl" -ForegroundColor Green
Write-Host "=" * 80

# Test data
$testData = @{
    url = $imageUrl
} | ConvertTo-Json

Write-Host "`n1. Testing /analyze endpoint..." -ForegroundColor Yellow
try {
    $analyzeResponse = Invoke-RestMethod -Uri "$baseUrl/analyze" -Method POST -Body $testData -ContentType "application/json"
    Write-Host "✅ /analyze endpoint successful" -ForegroundColor Green
    Write-Host "Basic Analysis Results:" -ForegroundColor Cyan
    Write-Host "- Blurry: $($analyzeResponse.quality.blurry)"
    Write-Host "- Overexposed: $($analyzeResponse.quality.overexposed)"
    Write-Host "- Oversaturated: $($analyzeResponse.quality.oversaturated)"
    Write-Host "- Valid: $($analyzeResponse.quality.is_valid)"
    Write-Host "- Laplacian Variance: $($analyzeResponse.metrics.laplacian_var)"
    Write-Host "- Brightness: $($analyzeResponse.metrics.brightness)"
    Write-Host "- Avg Saturation: $($analyzeResponse.metrics.avg_saturation)"
} catch {
    Write-Host "❌ /analyze endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n2. Testing /detailed-analyze endpoint..." -ForegroundColor Yellow
try {
    $detailedResponse = Invoke-RestMethod -Uri "$baseUrl/detailed-analyze" -Method POST -Body $testData -ContentType "application/json"
    Write-Host "✅ /detailed-analyze endpoint successful" -ForegroundColor Green
    Write-Host "Detailed Analysis Results:" -ForegroundColor Cyan
    Write-Host "- Blurry: $($detailedResponse.quality_analysis.blurry)"
    Write-Host "- Overexposed: $($detailedResponse.quality_analysis.overexposed)"
    Write-Host "- Oversaturated: $($detailedResponse.quality_analysis.oversaturated)"
    Write-Host "- Valid: $($detailedResponse.quality_analysis.is_valid)"
    Write-Host "- Laplacian Variance: $($detailedResponse.raw_metrics.laplacian_variance)"
    Write-Host "- Brightness: $($detailedResponse.raw_metrics.brightness)"
    Write-Host "- Avg Saturation: $($detailedResponse.raw_metrics.average_saturation)"
} catch {
    Write-Host "❌ /detailed-analyze endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n3. Comparing Results..." -ForegroundColor Yellow
Write-Host "=" * 50

# Compare key metrics
$basicBlurry = $analyzeResponse.quality.blurry
$detailedBlurry = $detailedResponse.quality_analysis.blurry

$basicOverexposed = $analyzeResponse.quality.overexposed
$detailedOverexposed = $detailedResponse.quality_analysis.overexposed

$basicOversaturated = $analyzeResponse.quality.oversaturated
$detailedOversaturated = $detailedResponse.quality_analysis.oversaturated

$basicValid = $analyzeResponse.quality.is_valid
$detailedValid = $detailedResponse.quality_analysis.is_valid

$basicLaplacian = $analyzeResponse.metrics.laplacian_var
$detailedLaplacian = $detailedResponse.raw_metrics.laplacian_variance

$basicBrightness = $analyzeResponse.metrics.brightness
$detailedBrightness = $detailedResponse.raw_metrics.brightness

Write-Host "Quality Flags Comparison:"
Write-Host "- Blurry: Basic=$basicBlurry, Detailed=$detailedBlurry $(if ($basicBlurry -eq $detailedBlurry) { '✅' } else { '❌' })"
Write-Host "- Overexposed: Basic=$basicOverexposed, Detailed=$detailedOverexposed $(if ($basicOverexposed -eq $detailedOverexposed) { '✅' } else { '❌' })"
Write-Host "- Oversaturated: Basic=$basicOversaturated, Detailed=$detailedOversaturated $(if ($basicOversaturated -eq $detailedOversaturated) { '✅' } else { '❌' })"
Write-Host "- Valid: Basic=$basicValid, Detailed=$detailedValid $(if ($basicValid -eq $detailedValid) { '✅' } else { '❌' })"

Write-Host "`nMetric Values Comparison:"
Write-Host "- Laplacian Variance: Basic=$basicLaplacian, Detailed=$detailedLaplacian $(if ($basicLaplacian -eq $detailedLaplacian) { '✅' } else { '❌' })"
Write-Host "- Brightness: Basic=$basicBrightness, Detailed=$detailedBrightness $(if ($basicBrightness -eq $detailedBrightness) { '✅' } else { '❌' })"

# Overall assessment
$allMatch = ($basicBlurry -eq $detailedBlurry) -and 
           ($basicOverexposed -eq $detailedOverexposed) -and 
           ($basicOversaturated -eq $detailedOversaturated) -and 
           ($basicValid -eq $detailedValid) -and
           ($basicLaplacian -eq $detailedLaplacian) -and
           ($basicBrightness -eq $detailedBrightness)

Write-Host "`n" + "=" * 50
if ($allMatch) {
    Write-Host "🎉 SUCCESS: Both endpoints now return consistent results!" -ForegroundColor Green
} else {
    Write-Host "⚠️  ISSUE: Endpoints still return different results" -ForegroundColor Red
    Write-Host "This indicates the fix may need further refinement." -ForegroundColor Yellow
}

Write-Host "`nTest completed." -ForegroundColor Green
