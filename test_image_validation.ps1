# Image Validation Test Script
# Tests all provided URLs against expected results

$testData = @(
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6ab133657e6a8000a2e931d77363"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a46f99673fa8000440c661ed431"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a459d7271b880008c9526b7cba1"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a35a7ea71b08000e235826ae27f"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a351b1b715d800002a3d754a14e"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a1c63b377698000ab8757617939"; expected=$true},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a1af861760d8000b2dbe6d8198d"; expected=$true},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a19a1b67880800039f6fe4ba3d8"; expected=$true},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a17f6a7729b8000e33d7cabaf56"; expected=$true},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a10018a79f080005bd609edf753"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a0be982743c8000bd7807866342"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a0bc9a87f9b8000d7145e0fbf35"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b6a0819e678578000d987195d6501"; expected=$false},
    @{url="https://imagedetectionv2.blob.core.windows.net/images/068b69fbc27176dc8000a85c381f16bd"; expected=$false}
)

$results = @()
$correctPredictions = 0
$totalTests = $testData.Count

Write-Host "Testing $totalTests images..." -ForegroundColor Green
Write-Host "Expected: FALSE = No Issues, TRUE = Has Issues" -ForegroundColor Yellow
Write-Host ""

foreach ($test in $testData) {
    $body = @{
        url = $test.url
        analysis_mode = "comprehensive"
        include_performance = $true
        include_raw_metrics = $true
    } | ConvertTo-Json -Depth 3
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8080/detailed-analyze" -Method POST -Body $body -ContentType "application/json"
        
        # Determine if API detected issues (is_valid = false means has issues)
        $apiDetectedIssues = -not $response.quality_analysis.is_valid
        $expectedIssues = $test.expected
        
        $isCorrect = $apiDetectedIssues -eq $expectedIssues
        if ($isCorrect) { $correctPredictions++ }
        
        $status = if ($isCorrect) { "[CORRECT]" } else { "[INCORRECT]" }
        $color = if ($isCorrect) { "Green" } else { "Red" }
        
        Write-Host "$status - Expected: $expectedIssues, API: $apiDetectedIssues" -ForegroundColor $color
        Write-Host "  URL: $($test.url.Split('/')[-1])" -ForegroundColor Gray
        Write-Host "  Quality Score: $([math]::Round($response.quality_analysis.overall_quality_score, 1))" -ForegroundColor Cyan
        Write-Host "  Sharpness: $([math]::Round($response.quality_analysis.sharpness_score, 1)) | Exposure: $([math]::Round($response.quality_analysis.exposure_score, 1)) | Color: $([math]::Round($response.quality_analysis.color_score, 1))" -ForegroundColor Cyan
        Write-Host "  Raw Metrics: LaplacianVar=$([math]::Round($response.raw_metrics.laplacian_variance, 2)), Brightness=$([math]::Round($response.raw_metrics.brightness, 1))" -ForegroundColor Gray
        Write-Host "  Quality Flags: Blur=$($response.quality_analysis.blurry), Overexposed=$($response.quality_analysis.overexposed), Oversaturated=$($response.quality_analysis.oversaturated)" -ForegroundColor Gray
        Write-Host "  Processing Time: $([math]::Round($response.processing_time_sec, 3))s" -ForegroundColor Yellow
        
        # Print detailed response for first few tests
        if ($results.Count -lt 3) {
            Write-Host "  === DETAILED RESPONSE ===" -ForegroundColor Magenta
            Write-Host "  Image Metadata: $($response.image_metadata.width)x$($response.image_metadata.height) $($response.image_metadata.format)" -ForegroundColor White
            Write-Host "  Overall Assessment: Grade=$($response.overall_assessment.quality_grade), Usability=$([math]::Round($response.overall_assessment.usability_score, 1))" -ForegroundColor White
            if ($response.quality_checks -and $response.quality_checks.Count -gt 0) {
                Write-Host "  Quality Checks: $($response.quality_checks.Count) checks performed" -ForegroundColor White
                foreach ($check in $response.quality_checks[0..2]) {
                    $checkStatus = if ($check.passed) { "PASS" } else { "FAIL" }
                    Write-Host "    - $($check.check_name): $checkStatus (confidence: $([math]::Round($check.confidence, 2)))" -ForegroundColor White
                }
            }
        }
        Write-Host ""
        
        $results += @{
            url = $test.url
            expected = $expectedIssues
            apiResult = $apiDetectedIssues
            correct = $isCorrect
            laplacianVar = $response.raw_metrics.laplacian_variance
            blurry = $response.quality_analysis.blurry
            overexposed = $response.quality_analysis.overexposed
            oversaturated = $response.quality_analysis.oversaturated
            isValid = $response.quality_analysis.is_valid
            qualityScore = $response.quality_analysis.overall_quality_score
            sharpnessScore = $response.quality_analysis.sharpness_score
            exposureScore = $response.quality_analysis.exposure_score
            colorScore = $response.quality_analysis.color_score
            processingTime = $response.processing_time_sec
        }
    }
    catch {
        Write-Host "[ERROR] - Failed to test $($test.url)" -ForegroundColor Red
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

# Summary
Write-Host "=== SUMMARY ===" -ForegroundColor Cyan
Write-Host "Correct Predictions: $correctPredictions / $totalTests" -ForegroundColor Green
Write-Host "Accuracy: $([math]::Round(($correctPredictions / $totalTests) * 100, 2))%" -ForegroundColor Green

# Show incorrect predictions with detailed analysis
$incorrectResults = $results | Where-Object { -not $_.correct }
if ($incorrectResults.Count -gt 0) {
    Write-Host ""
    Write-Host "=== INCORRECT PREDICTIONS ===" -ForegroundColor Red
    foreach ($result in $incorrectResults) {
        Write-Host "URL: $($result.url.Split('/')[-1])" -ForegroundColor Yellow
        Write-Host "  Expected Issues: $($result.expected), API Detected: $($result.apiResult)" -ForegroundColor Red
        Write-Host "  Quality Scores: Overall=$([math]::Round($result.qualityScore, 1)), Sharpness=$([math]::Round($result.sharpnessScore, 1)), Exposure=$([math]::Round($result.exposureScore, 1))" -ForegroundColor Gray
        Write-Host "  Raw Metrics: LaplacianVar=$([math]::Round($result.laplacianVar, 2)), Quality Flags: Blur=$($result.blurry), Overexposed=$($result.overexposed)" -ForegroundColor Gray
        Write-Host "  Processing Time: $([math]::Round($result.processingTime, 3))s" -ForegroundColor Gray
    }
}

# Performance Statistics
if ($results.Count -gt 0) {
    $avgProcessingTime = ($results | Measure-Object -Property processingTime -Average).Average
    $avgQualityScore = ($results | Measure-Object -Property qualityScore -Average).Average
    Write-Host ""
    Write-Host "=== PERFORMANCE STATISTICS ===" -ForegroundColor Cyan
    Write-Host "Average Processing Time: $([math]::Round($avgProcessingTime, 3))s" -ForegroundColor Green
    Write-Host "Average Quality Score: $([math]::Round($avgQualityScore, 1))" -ForegroundColor Green
    Write-Host "Total Images Processed: $($results.Count)" -ForegroundColor Green
}