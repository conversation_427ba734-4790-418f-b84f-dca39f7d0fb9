package analyzer

import (
	"image"
	"image/color"
	"math"
	"testing"
)

func TestNewMetricsCalculator(t *testing.T) {
	calc := NewMetricsCalculator()
	if calc == nil {
		t.Fatal("Expected non-nil metrics calculator")
	}
}

func TestCalculateBasicMetrics(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create a uniform gray image
	img := createTestImage(100, 100, color.RGBA{128, 128, 128, 255})

	metrics := calc.CalculateBasicMetrics(img)

	// For a uniform gray image, all RGB values should be equal
	expectedValue := 128.0 / 255.0 // Normalized to [0,1]
	tolerance := 0.01

	if math.Abs(metrics.avgR-expectedValue) > tolerance {
		t.Errorf("Expected avgR ~%f, got %f", expectedValue, metrics.avgR)
	}
	if math.Abs(metrics.avgG-expectedValue) > tolerance {
		t.Errorf("Expected avgG ~%f, got %f", expectedValue, metrics.avgG)
	}
	if math.Abs(metrics.avgB-expectedValue) > tolerance {
		t.<PERSON>("Expected avgB ~%f, got %f", expectedValue, metrics.avgB)
	}

	// For gray image, saturation should be low
	if metrics.avgSaturation > 0.1 {
		t.Errorf("Expected low saturation for gray image, got %f", metrics.avgSaturation)
	}

	// Luminance should be around the expected value
	if math.Abs(metrics.avgLuminance-expectedValue) > tolerance {
		t.Errorf("Expected avgLuminance ~%f, got %f", expectedValue, metrics.avgLuminance)
	}
}

func TestCalculateBasicMetrics_ColoredImage(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create a pure red image
	img := createTestImage(100, 100, color.RGBA{255, 0, 0, 255})

	metrics := calc.CalculateBasicMetrics(img)

	// Red channel should be 1.0, others should be 0.0
	if math.Abs(metrics.avgR-1.0) > 0.01 {
		t.Errorf("Expected avgR ~1.0, got %f", metrics.avgR)
	}
	if metrics.avgG > 0.01 {
		t.Errorf("Expected avgG ~0.0, got %f", metrics.avgG)
	}
	if metrics.avgB > 0.01 {
		t.Errorf("Expected avgB ~0.0, got %f", metrics.avgB)
	}

	// Red image should have high saturation
	if metrics.avgSaturation < 0.9 {
		t.Errorf("Expected high saturation for red image, got %f", metrics.avgSaturation)
	}
}

func TestCalculateLaplacianVariance(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create a uniform image (should have low variance)
	gray := image.NewGray(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			gray.Set(x, y, color.Gray{128})
		}
	}

	variance := calc.CalculateLaplacianVariance(gray)

	// Uniform image should have very low variance
	if variance > 10 {
		t.Errorf("Expected low variance for uniform image, got %f", variance)
	}
}

func TestCalculateLaplacianVariance_EdgeImage(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create an image with sharp edges
	gray := image.NewGray(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			if x < 50 {
				gray.Set(x, y, color.Gray{0})   // Black half
			} else {
				gray.Set(x, y, color.Gray{255}) // White half
			}
		}
	}

	variance := calc.CalculateLaplacianVariance(gray)

	// Image with edges should have higher variance
	if variance < 100 {
		t.Errorf("Expected higher variance for edge image, got %f", variance)
	}
}

func TestCalculateBrightness(t *testing.T) {
	calc := NewMetricsCalculator()

	testCases := []struct {
		name           string
		grayValue      uint8
		expectedBright float64
	}{
		{"Black Image", 0, 0.0},
		{"Gray Image", 128, 128.0},
		{"White Image", 255, 255.0},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			gray := image.NewGray(image.Rect(0, 0, 50, 50))
			for y := 0; y < 50; y++ {
				for x := 0; x < 50; x++ {
					gray.Set(x, y, color.Gray{tc.grayValue})
				}
			}

			brightness := calc.CalculateBrightness(gray)

			if math.Abs(brightness-tc.expectedBright) > 1.0 {
				t.Errorf("Expected brightness ~%f, got %f", tc.expectedBright, brightness)
			}
		})
	}
}

func TestDetectSkew(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create a simple image with some edges
	gray := image.NewGray(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			if y < 50 {
				gray.Set(x, y, color.Gray{0})   // Top half black
			} else {
				gray.Set(x, y, color.Gray{255}) // Bottom half white
			}
		}
	}

	skewAngle := calc.DetectSkew(gray)

	// Should detect some skew angle (or nil if not enough edges)
	if skewAngle != nil {
		// Angle should be within reasonable range
		if *skewAngle < -45 || *skewAngle > 45 {
			t.Errorf("Skew angle out of expected range: %f", *skewAngle)
		}
	}
}

func TestDetectContours(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create a uniform image (should have few contours)
	gray := image.NewGray(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			gray.Set(x, y, color.Gray{128})
		}
	}

	contours := calc.DetectContours(gray)

	// Uniform image should have very few contours
	if contours > 10 {
		t.Errorf("Expected few contours for uniform image, got %d", contours)
	}
}

func TestDetectContours_ComplexImage(t *testing.T) {
	calc := NewMetricsCalculator()

	// Create an image with multiple regions
	gray := image.NewGray(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			// Create a checkerboard pattern
			if (x/10+y/10)%2 == 0 {
				gray.Set(x, y, color.Gray{0})
			} else {
				gray.Set(x, y, color.Gray{255})
			}
		}
	}

	contours := calc.DetectContours(gray)

	// Checkerboard should have many contours
	if contours < 10 {
		t.Errorf("Expected many contours for checkerboard image, got %d", contours)
	}
}

func TestRgbToHSV(t *testing.T) {
	calc := NewMetricsCalculator().(*metricsCalculator)

	testCases := []struct {
		name string
		r, g, b float64
		expectedH, expectedS, expectedV float64
		tolerance float64
	}{
		{"Pure Red", 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0},
		{"Pure Green", 0.0, 1.0, 0.0, 120.0, 1.0, 1.0, 5.0},
		{"Pure Blue", 0.0, 0.0, 1.0, 240.0, 1.0, 1.0, 5.0},
		{"White", 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0},
		{"Black", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0},
		{"Gray", 0.5, 0.5, 0.5, 0.0, 0.0, 0.5, 1.0},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			h, s, v := calc.rgbToHSV(tc.r, tc.g, tc.b)

			if math.Abs(h-tc.expectedH) > tc.tolerance {
				t.Errorf("Expected H ~%f, got %f", tc.expectedH, h)
			}
			if math.Abs(s-tc.expectedS) > 0.01 {
				t.Errorf("Expected S ~%f, got %f", tc.expectedS, s)
			}
			if math.Abs(v-tc.expectedV) > 0.01 {
				t.Errorf("Expected V ~%f, got %f", tc.expectedV, v)
			}
		})
	}
}

func TestCalculateSkewAngle(t *testing.T) {
	calc := NewMetricsCalculator().(*metricsCalculator)

	// Test with horizontal line coordinates
	horizontalCoords := [][2]int{
		{10, 50}, {20, 50}, {30, 50}, {40, 50}, {50, 50},
	}

	angle := calc.calculateSkewAngle(horizontalCoords, 100, 100)

	// Horizontal line should have angle close to 0
	if math.Abs(angle) > 5 {
		t.Errorf("Expected angle close to 0 for horizontal line, got %f", angle)
	}
}

func TestCalculateSkewAngle_EmptyCoords(t *testing.T) {
	calc := NewMetricsCalculator().(*metricsCalculator)

	// Test with empty coordinates
	emptyCoords := [][2]int{}
	angle := calc.calculateSkewAngle(emptyCoords, 100, 100)

	// Should return 0 for empty coordinates
	if angle != 0 {
		t.Errorf("Expected angle 0 for empty coordinates, got %f", angle)
	}
}